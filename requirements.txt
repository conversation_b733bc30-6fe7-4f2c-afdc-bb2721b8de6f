# Web Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0

# MCP Protocol
mcp==1.0.0

# Computer Vision
opencv-python==4.8.1.78
pillow==10.1.0

# AI/ML
torch==2.1.1
torchvision==0.16.1
transformers==4.35.2
openai==1.3.7

# Audio Processing
openai-whisper==20231117

# OCR
pytesseract==0.3.10
easyocr>=1.7.0

# Object Detection
ultralytics>=8.0.0

# File System Monitoring
watchdog>=3.0.0

# System Monitoring
psutil>=5.9.0

# Configuration
pyyaml>=6.0.0
python-dotenv>=1.0.0

# Logging
colorlog>=6.7.0

# Async HTTP
aiohttp>=3.9.0
websockets>=12.0
asyncio-mqtt>=0.16.0

# JSON-RPC
jsonrpc-base>=2.2.0

# CLI Tools
click>=8.1.0
rich>=13.0.0
tqdm>=4.66.0

# Pydantic for data validation
pydantic>=2.5.0

# Data Processing
numpy==1.24.3
pandas==2.1.3

# HTTP Client
httpx==0.25.2
aiohttp==3.9.1

# Configuration
pydantic==2.5.0
python-dotenv==1.0.0

# Security
cryptography==41.0.8
python-jose[cryptography]==3.3.0

# Logging
loguru==0.7.2

# File System
watchdog==3.0.0

# Async
asyncio-mqtt==0.16.1
